--[[
	Main Client Script for Tetris-like Puzzle Game
	Initializes client-side systems and UI

	Author: Augment Agent
	Created: 2025-06-25
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer

print("Tetris Game Client Starting...")

-- Wait for modules to load
local Modules = ReplicatedStorage:WaitForChild("Modules")
local Events = ReplicatedStorage:WaitForChild("Events")

-- Initialize client systems
local ShapeGenerator = require(Modules:WaitForChild("ShapeGenerator"))
local BlockShapes = require(Modules:WaitForChild("BlockShapes"))

-- Create a simple UI for testing
local function createTestUI()
	local screenGui = Instance.new("ScreenGui")
	screenGui.Name = "TetrisGameUI"
	screenGui.Parent = player.PlayerGui

	-- Score display
	local scoreFrame = Instance.new("Frame")
	scoreFrame.Name = "ScoreFrame"
	scoreFrame.Size = UDim2.new(0, 200, 0, 100)
	scoreFrame.Position = UDim2.new(0, 10, 0, 10)
	scoreFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
	scoreFrame.BorderSizePixel = 2
	scoreFrame.Parent = screenGui

	local scoreLabel = Instance.new("TextLabel")
	scoreLabel.Name = "ScoreLabel"
	scoreLabel.Size = UDim2.new(1, 0, 0.5, 0)
	scoreLabel.Position = UDim2.new(0, 0, 0, 0)
	scoreLabel.BackgroundTransparency = 1
	scoreLabel.Text = "Score: 0"
	scoreLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	scoreLabel.TextScaled = true
	scoreLabel.Font = Enum.Font.SourceSansBold
	scoreLabel.Parent = scoreFrame

	local highScoreLabel = Instance.new("TextLabel")
	highScoreLabel.Name = "HighScoreLabel"
	highScoreLabel.Size = UDim2.new(1, 0, 0.5, 0)
	highScoreLabel.Position = UDim2.new(0, 0, 0.5, 0)
	highScoreLabel.BackgroundTransparency = 1
	highScoreLabel.Text = "High Score: 0"
	highScoreLabel.TextColor3 = Color3.fromRGB(255, 255, 100)
	highScoreLabel.TextScaled = true
	highScoreLabel.Font = Enum.Font.SourceSans
	highScoreLabel.Parent = scoreFrame

	-- Shape preview area
	local previewFrame = Instance.new("Frame")
	previewFrame.Name = "PreviewFrame"
	previewFrame.Size = UDim2.new(0, 150, 0, 200)
	previewFrame.Position = UDim2.new(1, -160, 0, 10)
	previewFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	previewFrame.BorderSizePixel = 2
	previewFrame.Parent = screenGui

	local previewTitle = Instance.new("TextLabel")
	previewTitle.Name = "PreviewTitle"
	previewTitle.Size = UDim2.new(1, 0, 0, 30)
	previewTitle.Position = UDim2.new(0, 0, 0, 0)
	previewTitle.BackgroundTransparency = 1
	previewTitle.Text = "Next Shapes"
	previewTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
	previewTitle.TextScaled = true
	previewTitle.Font = Enum.Font.SourceSansBold
	previewTitle.Parent = previewFrame

	-- Test shape button
	local testButton = Instance.new("TextButton")
	testButton.Name = "TestShapeButton"
	testButton.Size = UDim2.new(0, 120, 0, 40)
	testButton.Position = UDim2.new(0, 10, 0, 120)
	testButton.BackgroundColor3 = Color3.fromRGB(0, 150, 0)
	testButton.Text = "Get Random Shape"
	testButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	testButton.TextScaled = true
	testButton.Font = Enum.Font.SourceSansBold
	testButton.Parent = screenGui

	-- Connect test button
	testButton.MouseButton1Click:Connect(function()
		local shape, shapeName = BlockShapes:getRandomShape()
		print("Random shape generated:", shapeName)

		-- Fire event to set current shape for dragging
		local Events = require(ReplicatedStorage:WaitForChild("Events"))
		Events:fire("ShapeSelected", shape, shapeName)
	end)

	return screenGui
end

-- Create the test UI
local gameUI = createTestUI()

-- Connect to server events
local Events = require(ReplicatedStorage:WaitForChild("Events"))

Events:connectRemoteEvent("ScoreUpdate", function(score, highScore)
	local scoreLabel = gameUI:FindFirstChild("ScoreFrame"):FindFirstChild("ScoreLabel")
	local highScoreLabel = gameUI:FindFirstChild("ScoreFrame"):FindFirstChild("HighScoreLabel")

	if scoreLabel then
		scoreLabel.Text = "Score: " .. score
	end
	if highScoreLabel then
		highScoreLabel.Text = "High Score: " .. highScore
	end
end)

Events:connectRemoteEvent("GameOver", function(gameData)
	print("Game Over! Final Score:", gameData.finalScore)

	-- Show game over screen
	local gameOverFrame = Instance.new("Frame")
	gameOverFrame.Name = "GameOverFrame"
	gameOverFrame.Size = UDim2.new(0, 300, 0, 200)
	gameOverFrame.Position = UDim2.new(0.5, -150, 0.5, -100)
	gameOverFrame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
	gameOverFrame.BackgroundTransparency = 0.3
	gameOverFrame.BorderSizePixel = 3
	gameOverFrame.BorderColor3 = Color3.fromRGB(255, 0, 0)
	gameOverFrame.Parent = gameUI

	local gameOverLabel = Instance.new("TextLabel")
	gameOverLabel.Size = UDim2.new(1, 0, 0.5, 0)
	gameOverLabel.Position = UDim2.new(0, 0, 0, 0)
	gameOverLabel.BackgroundTransparency = 1
	gameOverLabel.Text = "GAME OVER"
	gameOverLabel.TextColor3 = Color3.fromRGB(255, 0, 0)
	gameOverLabel.TextScaled = true
	gameOverLabel.Font = Enum.Font.SourceSansBold
	gameOverLabel.Parent = gameOverFrame

	local finalScoreLabel = Instance.new("TextLabel")
	finalScoreLabel.Size = UDim2.new(1, 0, 0.5, 0)
	finalScoreLabel.Position = UDim2.new(0, 0, 0.5, 0)
	finalScoreLabel.BackgroundTransparency = 1
	finalScoreLabel.Text = "Final Score: " .. gameData.finalScore
	finalScoreLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	finalScoreLabel.TextScaled = true
	finalScoreLabel.Font = Enum.Font.SourceSans
	finalScoreLabel.Parent = gameOverFrame

	-- Auto-hide after 3 seconds
	wait(3)
	gameOverFrame:Destroy()
end)

print("Tetris Game Client Initialized!")