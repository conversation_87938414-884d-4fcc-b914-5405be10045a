--[[
	DragDropHandler Client Script for Tetris-like Puzzle Game
	Handles mouse/touch input for dragging shapes and visual feedback
	
	Author: Augment Agent
	Created: 2025-06-25
]]

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- Wait for modules to load
local Modules = ReplicatedStorage:WaitForChild("Modules")
local Grid = require(Modules:WaitForChild("Grid"))
local BlockShapes = require(Modules:WaitForChild("BlockShapes"))
local Events = require(ReplicatedStorage:WaitForChild("Events"))

-- Initialize events
Events:initializeRemoteEvents()
Events:initializeBindableEvents()

local DragDropHandler = {}

-- State variables
local isDragging = false
local currentShape = nil
local currentShapeName = nil
local dragStartPosition = nil
local previewParts = {}
local highlightParts = {}
local dragConnection = nil
local grid = Grid.new()

-- Visual constants
local PREVIEW_COLOR = Color3.fromRGB(100, 255, 100) -- Green for valid placement
local INVALID_COLOR = Color3.fromRGB(255, 100, 100) -- Red for invalid placement
local HIGHLIGHT_COLOR = Color3.fromRGB(255, 255, 100) -- Yellow for hover
local PREVIEW_TRANSPARENCY = 0.5

-- Initialize the drag and drop system
function DragDropHandler:initialize()
	-- Connect input events
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		if input.UserInputType == Enum.UserInputType.MouseButton1 or 
		   input.UserInputType == Enum.UserInputType.Touch then
			self:startDrag(input)
		end
	end)
	
	UserInputService.InputEnded:Connect(function(input, gameProcessed)
		if input.UserInputType == Enum.UserInputType.MouseButton1 or 
		   input.UserInputType == Enum.UserInputType.Touch then
			self:endDrag(input)
		end
	end)
	
	-- Connect to shape selection events
	Events:connect("ShapeSelected", function(shape, shapeName)
		self:setCurrentShape(shape, shapeName)
	end)
	
	print("DragDropHandler initialized")
end

-- Set the current shape for dragging
function DragDropHandler:setCurrentShape(shape, shapeName)
	currentShape = shape
	currentShapeName = shapeName
	print("Current shape set to:", shapeName)
end

-- Start dragging operation
function DragDropHandler:startDrag(input)
	if not currentShape then
		print("No shape selected for dragging")
		return
	end
	
	isDragging = true
	dragStartPosition = Vector3.new(mouse.Hit.Position.X, 0, mouse.Hit.Position.Z)
	
	-- Start drag update loop
	dragConnection = RunService.Heartbeat:Connect(function()
		self:updateDrag()
	end)
	
	print("Started dragging shape:", currentShapeName)
end

-- Update drag operation
function DragDropHandler:updateDrag()
	if not isDragging or not currentShape then
		return
	end
	
	-- Get current mouse position in world space
	local mousePosition = Vector3.new(mouse.Hit.Position.X, 0, mouse.Hit.Position.Z)
	
	-- Convert world position to grid coordinates
	local gridX, gridY = self:worldToGrid(mousePosition)
	
	-- Clear previous preview
	self:clearPreview()
	
	-- Check if placement is valid
	local canPlace = grid:canPlace(currentShape, gridX, gridY)
	
	-- Create preview with appropriate color
	self:createPreview(currentShape, gridX, gridY, canPlace)
	
	-- Highlight grid area
	self:highlightGridArea(gridX, gridY, canPlace)
end

-- End dragging operation
function DragDropHandler:endDrag(input)
	if not isDragging then
		return
	end
	
	isDragging = false
	
	-- Disconnect drag update
	if dragConnection then
		dragConnection:Disconnect()
		dragConnection = nil
	end
	
	-- Get final position
	local mousePosition = Vector3.new(mouse.Hit.Position.X, 0, mouse.Hit.Position.Z)
	local gridX, gridY = self:worldToGrid(mousePosition)
	
	-- Attempt to place shape
	if currentShape and grid:canPlace(currentShape, gridX, gridY) then
		self:attemptPlacement(gridX, gridY)
	else
		print("Cannot place shape at position:", gridX, gridY)
		self:showInvalidPlacementFeedback()
	end
	
	-- Clear all visual feedback
	self:clearPreview()
	self:clearHighlights()
	
	print("Ended dragging")
end

-- Attempt to place the shape on the server
function DragDropHandler:attemptPlacement(gridX, gridY)
	if not currentShape or not currentShapeName then
		warn("No shape to place")
		return
	end
	
	-- Send placement request to server
	Events:fireServer("PlaceShape", {
		shape = currentShape,
		shapeName = currentShapeName,
		gridX = gridX,
		gridY = gridY
	})
	
	print("Placement request sent for", currentShapeName, "at", gridX, gridY)
end

-- Convert world position to grid coordinates
function DragDropHandler:worldToGrid(worldPosition)
	local gridOrigin = Vector3.new(-16, 0, -16) -- Should match Grid module
	local tileSize = 4 -- Should match Grid module
	
	local relativeX = worldPosition.X - gridOrigin.X
	local relativeZ = worldPosition.Z - gridOrigin.Z
	
	local gridX = math.floor(relativeX / tileSize) + 1
	local gridY = math.floor(relativeZ / tileSize) + 1
	
	-- Clamp to grid bounds
	gridX = math.max(1, math.min(9, gridX))
	gridY = math.max(1, math.min(9, gridY))
	
	return gridX, gridY
end

-- Create visual preview of shape placement
function DragDropHandler:createPreview(shape, gridX, gridY, isValid)
	local color = isValid and PREVIEW_COLOR or INVALID_COLOR
	
	for _, offset in ipairs(shape) do
		local tileX = gridX + offset.x
		local tileY = gridY + offset.y
		
		-- Only create preview if within bounds
		if tileX >= 1 and tileX <= 9 and tileY >= 1 and tileY <= 9 then
			local position = grid:getTilePosition(tileX, tileY)
			if position then
				local part = self:createPreviewPart(position, color)
				table.insert(previewParts, part)
			end
		end
	end
end

-- Create a single preview part
function DragDropHandler:createPreviewPart(position, color)
	local part = Instance.new("Part")
	part.Name = "PreviewTile"
	part.Size = Vector3.new(3.8, 0.5, 3.8) -- Slightly smaller than actual tiles
	part.Position = position + Vector3.new(0, 1, 0) -- Hover above grid
	part.Anchored = true
	part.CanCollide = false
	part.Color = color
	part.Transparency = PREVIEW_TRANSPARENCY
	part.Material = Enum.Material.ForceField
	part.Parent = workspace
	
	-- Add glow effect
	local pointLight = Instance.new("PointLight")
	pointLight.Color = color
	pointLight.Brightness = 1
	pointLight.Range = 10
	pointLight.Parent = part
	
	return part
end

-- Highlight grid area around cursor
function DragDropHandler:highlightGridArea(centerX, centerY, isValid)
	local color = isValid and HIGHLIGHT_COLOR or INVALID_COLOR
	
	-- Highlight a 3x3 area around the cursor
	for x = centerX - 1, centerX + 1 do
		for y = centerY - 1, centerY + 1 do
			if x >= 1 and x <= 9 and y >= 1 and y <= 9 then
				local position = grid:getTilePosition(x, y)
				if position then
					local part = self:createHighlightPart(position, color)
					table.insert(highlightParts, part)
				end
			end
		end
	end
end

-- Create a highlight part
function DragDropHandler:createHighlightPart(position, color)
	local part = Instance.new("Part")
	part.Name = "HighlightTile"
	part.Size = Vector3.new(4, 0.1, 4)
	part.Position = position + Vector3.new(0, 0.05, 0)
	part.Anchored = true
	part.CanCollide = false
	part.Color = color
	part.Transparency = 0.8
	part.Material = Enum.Material.Neon
	part.Parent = workspace
	
	return part
end

-- Clear preview parts
function DragDropHandler:clearPreview()
	for _, part in ipairs(previewParts) do
		if part and part.Parent then
			part:Destroy()
		end
	end
	previewParts = {}
end

-- Clear highlight parts
function DragDropHandler:clearHighlights()
	for _, part in ipairs(highlightParts) do
		if part and part.Parent then
			part:Destroy()
		end
	end
	highlightParts = {}
end

-- Show feedback for invalid placement
function DragDropHandler:showInvalidPlacementFeedback()
	-- Create a brief red flash effect
	local screenGui = player.PlayerGui:FindFirstChild("ScreenGui")
	if not screenGui then
		screenGui = Instance.new("ScreenGui")
		screenGui.Name = "ScreenGui"
		screenGui.Parent = player.PlayerGui
	end
	
	local flashFrame = Instance.new("Frame")
	flashFrame.Name = "InvalidFlash"
	flashFrame.Size = UDim2.new(1, 0, 1, 0)
	flashFrame.Position = UDim2.new(0, 0, 0, 0)
	flashFrame.BackgroundColor3 = Color3.fromRGB(255, 0, 0)
	flashFrame.BackgroundTransparency = 0.8
	flashFrame.Parent = screenGui
	
	-- Tween the flash
	local tween = TweenService:Create(flashFrame, 
		TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{BackgroundTransparency = 1}
	)
	
	tween:Play()
	tween.Completed:Connect(function()
		flashFrame:Destroy()
	end)
end

-- Connect to server events
Events:connectRemoteEvent("UpdateGrid", function(gridData)
	-- Update local grid state when server sends updates
	if gridData then
		-- Update grid representation
		print("Grid updated from server")
	end
end)

Events:connectRemoteEvent("PlaceShape", function(success, message)
	if success then
		print("Shape placed successfully!")
		-- Clear current shape
		currentShape = nil
		currentShapeName = nil
	else
		print("Shape placement failed:", message)
		DragDropHandler:showInvalidPlacementFeedback()
	end
end)

-- Initialize the system
DragDropHandler:initialize()

return DragDropHandler
