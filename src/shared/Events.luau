--[[
	Events Module for Tetris-like Puzzle Game
	Centralizes all RemoteEvents and BindableEvents
	
	Author: Augment Agent
	Created: 2025-06-25
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local Events = {}

-- RemoteEvents (Client-Server Communication)
Events.RemoteEvents = {
	-- Game state events
	"GameStart",
	"GameOver",
	"GameRestart",
	"GamePause",
	
	-- Placement events
	"PlaceShape",
	"ValidatePlacement",
	
	-- Score events
	"ScoreUpdate",
	"StatsUpdate",
	
	-- UI events
	"UpdatePreview",
	"ShowGameOver",
	"UpdateGrid"
}

-- BindableEvents (Internal Communication)
Events.BindableEvents = {
	-- Grid events
	"GridUpdated",
	"LinesCleared",
	"TilePlaced",
	
	-- Shape events
	"ShapeGenerated",
	"ShapeSelected",
	"ShapeRotated",
	
	-- Game flow events
	"MoveCompleted",
	"ComboAchieved",
	"HighScoreReached"
}

-- Initialize all events (called by server on startup)
function Events:initializeRemoteEvents()
	local eventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
	if not eventsFolder then
		eventsFolder = Instance.new("Folder")
		eventsFolder.Name = "RemoteEvents"
		eventsFolder.Parent = ReplicatedStorage
	end
	
	-- Create RemoteEvents
	for _, eventName in ipairs(self.RemoteEvents) do
		if not eventsFolder:FindFirstChild(eventName) then
			local remoteEvent = Instance.new("RemoteEvent")
			remoteEvent.Name = eventName
			remoteEvent.Parent = eventsFolder
		end
	end
	
	return eventsFolder
end

-- Initialize BindableEvents (called by modules that need them)
function Events:initializeBindableEvents()
	local eventsFolder = ReplicatedStorage:FindFirstChild("BindableEvents")
	if not eventsFolder then
		eventsFolder = Instance.new("Folder")
		eventsFolder.Name = "BindableEvents"
		eventsFolder.Parent = ReplicatedStorage
	end
	
	-- Create BindableEvents
	for _, eventName in ipairs(self.BindableEvents) do
		if not eventsFolder:FindFirstChild(eventName) then
			local bindableEvent = Instance.new("BindableEvent")
			bindableEvent.Name = eventName
			bindableEvent.Parent = eventsFolder
		end
	end
	
	return eventsFolder
end

-- Get a RemoteEvent by name
function Events:getRemoteEvent(eventName)
	local eventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
	if not eventsFolder then
		warn("Events:getRemoteEvent - RemoteEvents folder not found. Call initializeRemoteEvents first.")
		return nil
	end
	
	local event = eventsFolder:FindFirstChild(eventName)
	if not event then
		warn("Events:getRemoteEvent - RemoteEvent not found:", eventName)
		return nil
	end
	
	return event
end

-- Get a BindableEvent by name
function Events:getBindableEvent(eventName)
	local eventsFolder = ReplicatedStorage:FindFirstChild("BindableEvents")
	if not eventsFolder then
		warn("Events:getBindableEvent - BindableEvents folder not found. Call initializeBindableEvents first.")
		return nil
	end
	
	local event = eventsFolder:FindFirstChild(eventName)
	if not event then
		warn("Events:getBindableEvent - BindableEvent not found:", eventName)
		return nil
	end
	
	return event
end

-- Fire a RemoteEvent to all clients (server-side)
function Events:fireAllClients(eventName, ...)
	local event = self:getRemoteEvent(eventName)
	if event then
		event:FireAllClients(...)
	end
end

-- Fire a RemoteEvent to a specific client (server-side)
function Events:fireClient(eventName, player, ...)
	local event = self:getRemoteEvent(eventName)
	if event then
		event:FireClient(player, ...)
	end
end

-- Fire a RemoteEvent to server (client-side)
function Events:fireServer(eventName, ...)
	local event = self:getRemoteEvent(eventName)
	if event then
		event:FireServer(...)
	end
end

-- Fire a BindableEvent
function Events:fire(eventName, ...)
	local event = self:getBindableEvent(eventName)
	if event then
		event:Fire(...)
	end
end

-- Connect to a RemoteEvent (client-side)
function Events:connectRemoteEvent(eventName, callback)
	local event = self:getRemoteEvent(eventName)
	if event then
		return event.OnClientEvent:Connect(callback)
	end
	return nil
end

-- Connect to a RemoteEvent (server-side)
function Events:connectRemoteEventServer(eventName, callback)
	local event = self:getRemoteEvent(eventName)
	if event then
		return event.OnServerEvent:Connect(callback)
	end
	return nil
end

-- Connect to a BindableEvent
function Events:connect(eventName, callback)
	local event = self:getBindableEvent(eventName)
	if event then
		return event.Event:Connect(callback)
	end
	return nil
end

-- Utility function to wait for an event
function Events:waitForEvent(eventName, timeout)
	local event = self:getBindableEvent(eventName)
	if not event then
		return nil
	end
	
	if timeout then
		local connection
		local result = nil
		local finished = false
		
		connection = event.Event:Connect(function(...)
			result = {...}
			finished = true
			connection:Disconnect()
		end)
		
		local startTime = tick()
		while not finished and (tick() - startTime) < timeout do
			wait(0.1)
		end
		
		if not finished then
			connection:Disconnect()
		end
		
		return result
	else
		return {event.Event:Wait()}
	end
end

-- Clean up all events (for testing or shutdown)
function Events:cleanup()
	local remoteEventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
	if remoteEventsFolder then
		remoteEventsFolder:Destroy()
	end
	
	local bindableEventsFolder = ReplicatedStorage:FindFirstChild("BindableEvents")
	if bindableEventsFolder then
		bindableEventsFolder:Destroy()
	end
end

-- Get list of all available events
function Events:getAvailableEvents()
	return {
		remoteEvents = self.RemoteEvents,
		bindableEvents = self.BindableEvents
	}
end

return Events
