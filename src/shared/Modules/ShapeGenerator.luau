--[[
	ShapeGenerator Module for Tetris-like Puzzle Game
	Manages shape queue and generation with no immediate duplicates
	
	Author: Augment Agent
	Created: 2025-06-25
]]

local BlockShapes = require(script.Parent.BlockShapes)

local ShapeGenerator = {}
ShapeGenerator.__index = ShapeGenerator

-- Constructor
function ShapeGenerator.new()
	local self = setmetatable({}, ShapeGenerator)
	
	-- Queue of upcoming shapes (stores shape data and names)
	self._shapeQueue = {}
	self._nameQueue = {}
	
	-- Track recently used shapes to prevent immediate duplicates
	self._recentShapes = {}
	self._maxRecentShapes = 3 -- Prevent same shape in last 3 generations
	
	-- Initialize queue with 3 shapes
	self:_fillQueue()
	
	return self
end

-- Get the next shape and advance the queue
function ShapeGenerator:getNextShape()
	if #self._shapeQueue == 0 then
		warn("ShapeGenerator:getNextShape - Queue is empty, refilling")
		self:_fillQueue()
	end
	
	-- Get the first shape from queue
	local shape = table.remove(self._shapeQueue, 1)
	local shapeName = table.remove(self._nameQueue, 1)
	
	-- Add a new shape to maintain queue size
	self:_addShapeToQueue()
	
	return shape, shapeName
end

-- Get preview of next 3 shapes without removing them
function ShapeGenerator:previewShapes()
	local previews = {}
	
	for i = 1, math.min(3, #self._shapeQueue) do
		table.insert(previews, {
			shape = self._shapeQueue[i],
			name = self._nameQueue[i],
			index = i
		})
	end
	
	return previews
end

-- Get a specific preview shape by index (1-3)
function ShapeGenerator:getPreviewShape(index)
	if index < 1 or index > #self._shapeQueue then
		warn("ShapeGenerator:getPreviewShape - Invalid index:", index)
		return nil
	end
	
	return self._shapeQueue[index], self._nameQueue[index]
end

-- Force regenerate the queue (useful for testing or special events)
function ShapeGenerator:regenerateQueue()
	self._shapeQueue = {}
	self._nameQueue = {}
	self._recentShapes = {}
	self:_fillQueue()
end

-- Get queue status for debugging
function ShapeGenerator:getQueueStatus()
	return {
		queueSize = #self._shapeQueue,
		recentShapes = self._recentShapes,
		nextShapes = self._nameQueue
	}
end

-- Private methods

-- Fill the queue to maintain 3 shapes
function ShapeGenerator:_fillQueue()
	while #self._shapeQueue < 3 do
		self:_addShapeToQueue()
	end
end

-- Add a single shape to the queue
function ShapeGenerator:_addShapeToQueue()
	local shape, shapeName = self:_generateUniqueShape()
	
	-- Store shape data with rotation state (for future rotation system)
	local shapeData = {
		tiles = shape,
		rotation = 0, -- Current rotation (0, 90, 180, 270)
		originalName = shapeName
	}
	
	table.insert(self._shapeQueue, shapeData.tiles)
	table.insert(self._nameQueue, shapeName)
	
	-- Update recent shapes tracking
	table.insert(self._recentShapes, shapeName)
	if #self._recentShapes > self._maxRecentShapes then
		table.remove(self._recentShapes, 1)
	end
end

-- Generate a shape that hasn't been used recently
function ShapeGenerator:_generateUniqueShape()
	local attempts = 0
	local maxAttempts = 20 -- Prevent infinite loops
	
	while attempts < maxAttempts do
		local shape, shapeName = BlockShapes:getRandomShape()
		
		-- Check if this shape was used recently
		if not self:_isShapeRecent(shapeName) then
			return shape, shapeName
		end
		
		attempts = attempts + 1
	end
	
	-- If we can't find a unique shape, just return a random one
	warn("ShapeGenerator:_generateUniqueShape - Could not find unique shape, returning random")
	return BlockShapes:getRandomShape()
end

-- Check if a shape name is in the recent shapes list
function ShapeGenerator:_isShapeRecent(shapeName)
	for _, recentName in ipairs(self._recentShapes) do
		if recentName == shapeName then
			return true
		end
	end
	return false
end

-- Future rotation system methods (prepared for expansion)

-- Rotate current shape in queue (for future use)
function ShapeGenerator:rotatePreviewShape(index, clockwise)
	if index < 1 or index > #self._shapeQueue then
		warn("ShapeGenerator:rotatePreviewShape - Invalid index:", index)
		return false
	end
	
	local currentShape = self._shapeQueue[index]
	local rotatedShape
	
	if clockwise then
		rotatedShape = BlockShapes:rotateShape(currentShape)
	else
		-- Rotate 3 times clockwise for counter-clockwise
		rotatedShape = currentShape
		for i = 1, 3 do
			rotatedShape = BlockShapes:rotateShape(rotatedShape)
		end
	end
	
	self._shapeQueue[index] = rotatedShape
	return true
end

-- Get shape with specific rotation
function ShapeGenerator:getShapeWithRotation(shapeName, rotation)
	local shape = BlockShapes:getShape(shapeName)
	if not shape then
		return nil
	end
	
	-- Apply rotation (0, 90, 180, 270 degrees)
	local rotations = math.floor(rotation / 90) % 4
	for i = 1, rotations do
		shape = BlockShapes:rotateShape(shape)
	end
	
	return shape
end

-- Utility method to clear recent shapes (for testing)
function ShapeGenerator:clearRecentShapes()
	self._recentShapes = {}
end

-- Set the maximum number of recent shapes to track
function ShapeGenerator:setMaxRecentShapes(max)
	if max < 0 then
		warn("ShapeGenerator:setMaxRecentShapes - Max must be non-negative")
		return
	end
	
	self._maxRecentShapes = max
	
	-- Trim recent shapes if necessary
	while #self._recentShapes > self._maxRecentShapes do
		table.remove(self._recentShapes, 1)
	end
end

return ShapeGenerator
