--[[
	ScoreManager Module for Tetris-like Puzzle Game
	Manages scoring system with RemoteEvent communication
	
	Author: Augment Agent
	Created: 2025-06-25
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local ScoreManager = {}
ScoreManager.__index = ScoreManager

-- Scoring constants
local POINTS_PER_TILE = 10
local POINTS_PER_LINE = 100
local LINE_MULTIPLIERS = {
	[1] = 1,    -- Single line
	[2] = 2.5,  -- Double line
	[3] = 5,    -- Triple line
	[4] = 10,   -- Quad line (rare but possible)
	[5] = 20,   -- Quintuple line (very rare)
}

-- Constructor
function ScoreManager.new()
	local self = setmetatable({}, ScoreManager)
	
	-- Score tracking
	self._currentScore = 0
	self._highScore = 0
	self._totalTilesPlaced = 0
	self._totalLinesCleared = 0
	self._gameStartTime = tick()
	
	-- Statistics tracking
	self._stats = {
		shapesPlaced = 0,
		singleLines = 0,
		multiLines = 0,
		longestCombo = 0,
		currentCombo = 0,
		averageTimePerMove = 0,
		lastMoveTime = tick()
	}
	
	-- RemoteEvents for client communication (will be created by server)
	self._scoreUpdateEvent = nil
	self._statsUpdateEvent = nil
	
	return self
end

-- Initialize RemoteEvents (called by server)
function ScoreManager:initializeRemoteEvents()
	-- Create RemoteEvents if they don't exist
	if not ReplicatedStorage:FindFirstChild("ScoreUpdate") then
		local scoreEvent = Instance.new("RemoteEvent")
		scoreEvent.Name = "ScoreUpdate"
		scoreEvent.Parent = ReplicatedStorage
	end
	
	if not ReplicatedStorage:FindFirstChild("StatsUpdate") then
		local statsEvent = Instance.new("RemoteEvent")
		statsEvent.Name = "StatsUpdate"
		statsEvent.Parent = ReplicatedStorage
	end
	
	self._scoreUpdateEvent = ReplicatedStorage:WaitForChild("ScoreUpdate")
	self._statsUpdateEvent = ReplicatedStorage:WaitForChild("StatsUpdate")
end

-- Add points to the score
function ScoreManager:add(points)
	if type(points) ~= "number" or points < 0 then
		warn("ScoreManager:add - Invalid points value:", points)
		return false
	end
	
	self._currentScore = self._currentScore + points
	
	-- Update high score if necessary
	if self._currentScore > self._highScore then
		self._highScore = self._currentScore
	end
	
	-- Fire RemoteEvent to update clients
	if self._scoreUpdateEvent then
		self._scoreUpdateEvent:FireAllClients(self._currentScore, self._highScore)
	end
	
	return true
end

-- Get current score
function ScoreManager:getScore()
	return self._currentScore
end

-- Get high score
function ScoreManager:getHighScore()
	return self._highScore
end

-- Score for placing tiles
function ScoreManager:scoreTilePlacement(tilesPlaced)
	if type(tilesPlaced) ~= "number" or tilesPlaced <= 0 then
		warn("ScoreManager:scoreTilePlacement - Invalid tiles count:", tilesPlaced)
		return 0
	end
	
	local points = tilesPlaced * POINTS_PER_TILE
	self._totalTilesPlaced = self._totalTilesPlaced + tilesPlaced
	self._stats.shapesPlaced = self._stats.shapesPlaced + 1
	
	-- Update timing statistics
	local currentTime = tick()
	local timeSinceLastMove = currentTime - self._stats.lastMoveTime
	self._stats.averageTimePerMove = (self._stats.averageTimePerMove * (self._stats.shapesPlaced - 1) + timeSinceLastMove) / self._stats.shapesPlaced
	self._stats.lastMoveTime = currentTime
	
	self:add(points)
	self:_updateStats()
	
	return points
end

-- Score for clearing lines
function ScoreManager:scoreLineClearing(linesCleared)
	if type(linesCleared) ~= "number" or linesCleared <= 0 then
		warn("ScoreManager:scoreLineClearing - Invalid lines count:", linesCleared)
		return 0
	end
	
	-- Calculate base points
	local basePoints = linesCleared * POINTS_PER_LINE
	
	-- Apply multiplier based on number of lines cleared simultaneously
	local multiplier = LINE_MULTIPLIERS[linesCleared] or 1
	local totalPoints = math.floor(basePoints * multiplier)
	
	-- Update statistics
	self._totalLinesCleared = self._totalLinesCleared + linesCleared
	if linesCleared == 1 then
		self._stats.singleLines = self._stats.singleLines + 1
	else
		self._stats.multiLines = self._stats.multiLines + 1
	end
	
	-- Update combo tracking
	self._stats.currentCombo = self._stats.currentCombo + 1
	if self._stats.currentCombo > self._stats.longestCombo then
		self._stats.longestCombo = self._stats.currentCombo
	end
	
	self:add(totalPoints)
	self:_updateStats()
	
	return totalPoints
end

-- Reset combo (called when no lines are cleared)
function ScoreManager:resetCombo()
	self._stats.currentCombo = 0
	self:_updateStats()
end

-- Get comprehensive game statistics
function ScoreManager:getStats()
	local gameTime = tick() - self._gameStartTime
	
	return {
		currentScore = self._currentScore,
		highScore = self._highScore,
		totalTilesPlaced = self._totalTilesPlaced,
		totalLinesCleared = self._totalLinesCleared,
		shapesPlaced = self._stats.shapesPlaced,
		singleLines = self._stats.singleLines,
		multiLines = self._stats.multiLines,
		longestCombo = self._stats.longestCombo,
		currentCombo = self._stats.currentCombo,
		gameTime = gameTime,
		averageTimePerMove = self._stats.averageTimePerMove,
		tilesPerSecond = gameTime > 0 and self._totalTilesPlaced / gameTime or 0,
		linesPerMinute = gameTime > 0 and (self._totalLinesCleared * 60) / gameTime or 0
	}
end

-- Reset score and statistics for new game
function ScoreManager:resetGame()
	self._currentScore = 0
	self._totalTilesPlaced = 0
	self._totalLinesCleared = 0
	self._gameStartTime = tick()
	
	self._stats = {
		shapesPlaced = 0,
		singleLines = 0,
		multiLines = 0,
		longestCombo = 0,
		currentCombo = 0,
		averageTimePerMove = 0,
		lastMoveTime = tick()
	}
	
	-- Fire RemoteEvent to update clients
	if self._scoreUpdateEvent then
		self._scoreUpdateEvent:FireAllClients(self._currentScore, self._highScore)
	end
	
	self:_updateStats()
end

-- Set high score (for loading saved data)
function ScoreManager:setHighScore(score)
	if type(score) ~= "number" or score < 0 then
		warn("ScoreManager:setHighScore - Invalid score value:", score)
		return false
	end
	
	self._highScore = score
	return true
end

-- Calculate score for a theoretical move (for AI or hints)
function ScoreManager:calculateMoveScore(tilesPlaced, linesCleared)
	local tilePoints = tilesPlaced * POINTS_PER_TILE
	local linePoints = 0
	
	if linesCleared > 0 then
		local basePoints = linesCleared * POINTS_PER_LINE
		local multiplier = LINE_MULTIPLIERS[linesCleared] or 1
		linePoints = math.floor(basePoints * multiplier)
	end
	
	return tilePoints + linePoints
end

-- Private methods

-- Update statistics and fire RemoteEvent
function ScoreManager:_updateStats()
	if self._statsUpdateEvent then
		self._statsUpdateEvent:FireAllClients(self:getStats())
	end
end

-- Get scoring constants (for UI display)
function ScoreManager:getScoringInfo()
	return {
		pointsPerTile = POINTS_PER_TILE,
		pointsPerLine = POINTS_PER_LINE,
		lineMultipliers = LINE_MULTIPLIERS
	}
end

return ScoreManager
