--[[
	Grid Module for Tetris-like Puzzle Game
	Manages a 9x9 grid system with placement logic
	
	Author: Augment Agent
	Created: 2025-06-25
]]

local Grid = {}
Grid.__index = Grid

-- Constants
local GRID_SIZE = 9
local TILE_SIZE = 4 -- Size of each visual tile in studs
local GRID_ORIGIN = Vector3.new(-16, 0, -16) -- World position of grid center

-- Constructor
function Grid.new()
	local self = setmetatable({}, Grid)
	
	-- Initialize 9x9 grid with all tiles unfilled (false)
	self._grid = {}
	for x = 1, GRID_SIZE do
		self._grid[x] = {}
		for y = 1, GRID_SIZE do
			self._grid[x][y] = false
		end
	end
	
	-- Store visual parts for cleanup
	self._visualParts = {}
	
	return self
end

-- Check if a tile is filled
function Grid:isFilled(x, y)
	if not self:_isValidCoordinate(x, y) then
		warn("Grid:isFilled - Invalid coordinates:", x, y)
		return false
	end
	
	return self._grid[x][y]
end

-- Set the filled state of a tile
function Grid:setFilled(x, y, state)
	if not self:_isValidCoordinate(x, y) then
		warn("Grid:setFilled - Invalid coordinates:", x, y)
		return false
	end
	
	self._grid[x][y] = state
	return true
end

-- Get world position for a grid coordinate
function Grid:getTilePosition(x, y)
	if not self:_isValidCoordinate(x, y) then
		warn("Grid:getTilePosition - Invalid coordinates:", x, y)
		return nil
	end
	
	local offsetX = (x - 1) * TILE_SIZE - (GRID_SIZE - 1) * TILE_SIZE / 2
	local offsetZ = (y - 1) * TILE_SIZE - (GRID_SIZE - 1) * TILE_SIZE / 2
	
	return GRID_ORIGIN + Vector3.new(offsetX, 0, offsetZ)
end

-- Check if a shape can be placed at the given position
function Grid:canPlace(shape, startX, startY)
	if not shape or type(shape) ~= "table" then
		warn("Grid:canPlace - Invalid shape provided")
		return false
	end
	
	-- Check each tile of the shape
	for _, offset in ipairs(shape) do
		local tileX = startX + offset.x
		local tileY = startY + offset.y
		
		-- Check bounds
		if not self:_isValidCoordinate(tileX, tileY) then
			return false
		end
		
		-- Check if tile is already filled
		if self:isFilled(tileX, tileY) then
			return false
		end
	end
	
	return true
end

-- Place a shape on the grid and create visual representation
function Grid:place(shape, startX, startY)
	if not self:canPlace(shape, startX, startY) then
		warn("Grid:place - Cannot place shape at position:", startX, startY)
		return false
	end
	
	local placedParts = {}
	
	-- Place each tile of the shape
	for _, offset in ipairs(shape) do
		local tileX = startX + offset.x
		local tileY = startY + offset.y
		
		-- Mark tile as filled
		self:setFilled(tileX, tileY, true)
		
		-- Create visual part
		local part = self:_createVisualTile(tileX, tileY)
		table.insert(placedParts, part)
		table.insert(self._visualParts, part)
	end
	
	return true, placedParts
end

-- Clear full lines and return number cleared
function Grid:clearFullLines()
	local linesCleared = 0
	local clearedParts = {}
	
	-- Check rows (horizontal lines)
	for y = 1, GRID_SIZE do
		local isFullRow = true
		for x = 1, GRID_SIZE do
			if not self:isFilled(x, y) then
				isFullRow = false
				break
			end
		end
		
		if isFullRow then
			linesCleared = linesCleared + 1
			-- Clear the row and collect visual parts
			for x = 1, GRID_SIZE do
				self:setFilled(x, y, false)
				local part = self:_findVisualPart(x, y)
				if part then
					table.insert(clearedParts, part)
					self:_removeVisualPart(part)
				end
			end
		end
	end
	
	-- Check columns (vertical lines)
	for x = 1, GRID_SIZE do
		local isFullColumn = true
		for y = 1, GRID_SIZE do
			if not self:isFilled(x, y) then
				isFullColumn = false
				break
			end
		end
		
		if isFullColumn then
			linesCleared = linesCleared + 1
			-- Clear the column and collect visual parts
			for y = 1, GRID_SIZE do
				self:setFilled(x, y, false)
				local part = self:_findVisualPart(x, y)
				if part then
					table.insert(clearedParts, part)
					self:_removeVisualPart(part)
				end
			end
		end
	end
	
	return linesCleared, clearedParts
end

-- Get all currently filled tile coordinates
function Grid:getFilledTiles()
	local filledTiles = {}
	
	for x = 1, GRID_SIZE do
		for y = 1, GRID_SIZE do
			if self:isFilled(x, y) then
				table.insert(filledTiles, {x = x, y = y})
			end
		end
	end
	
	return filledTiles
end

-- Private helper methods
function Grid:_isValidCoordinate(x, y)
	return x >= 1 and x <= GRID_SIZE and y >= 1 and y <= GRID_SIZE
end

function Grid:_createVisualTile(x, y)
	local part = Instance.new("Part")
	part.Name = "GridTile_" .. x .. "_" .. y
	part.Size = Vector3.new(TILE_SIZE - 0.1, 1, TILE_SIZE - 0.1) -- Small gap between tiles
	part.Position = self:getTilePosition(x, y) + Vector3.new(0, 0.5, 0)
	part.Anchored = true
	part.BrickColor = BrickColor.new("Bright blue")
	part.Material = Enum.Material.Neon
	part.Parent = workspace
	
	return part
end

function Grid:_findVisualPart(x, y)
	local targetName = "GridTile_" .. x .. "_" .. y
	for i, part in ipairs(self._visualParts) do
		if part.Name == targetName then
			return part, i
		end
	end
	return nil
end

function Grid:_removeVisualPart(part)
	for i, visualPart in ipairs(self._visualParts) do
		if visualPart == part then
			table.remove(self._visualParts, i)
			part:Destroy()
			break
		end
	end
end

-- Cleanup method
function Grid:destroy()
	for _, part in ipairs(self._visualParts) do
		if part and part.Parent then
			part:Destroy()
		end
	end
	self._visualParts = {}
end

return Grid
