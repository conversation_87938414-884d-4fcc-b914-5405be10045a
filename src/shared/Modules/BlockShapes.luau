--[[
	BlockShapes Module for Tetris-like Puzzle Game
	Contains predefined shapes and shape management
	
	Author: Augment Agent
	Created: 2025-06-25
]]

local BlockShapes = {}

-- Shape definitions using relative coordinates from pivot point (0,0)
-- Each shape is an array of {x, y} offsets
BlockShapes.SHAPES = {
	-- I-piece (4 tiles in a line)
	I_PIECE = {
		{x = 0, y = 0},
		{x = 1, y = 0},
		{x = 2, y = 0},
		{x = 3, y = 0}
	},
	
	-- L-piece (4 tiles in L shape)
	L_PIECE = {
		{x = 0, y = 0},
		{x = 0, y = 1},
		{x = 0, y = 2},
		{x = 1, y = 2}
	},
	
	-- Reverse L-piece
	L_PIECE_REVERSE = {
		{x = 1, y = 0},
		{x = 1, y = 1},
		{x = 1, y = 2},
		{x = 0, y = 2}
	},
	
	-- T-piece (4 tiles in T shape)
	T_PIECE = {
		{x = 0, y = 1},
		{x = 1, y = 0},
		{x = 1, y = 1},
		{x = 1, y = 2}
	},
	
	-- Square/O-piece (2x2 square)
	SQUARE = {
		{x = 0, y = 0},
		{x = 0, y = 1},
		{x = 1, y = 0},
		{x = 1, y = 1}
	},
	
	-- Z-piece (zigzag shape)
	Z_PIECE = {
		{x = 0, y = 0},
		{x = 1, y = 0},
		{x = 1, y = 1},
		{x = 2, y = 1}
	},
	
	-- S-piece (reverse zigzag)
	S_PIECE = {
		{x = 1, y = 0},
		{x = 2, y = 0},
		{x = 0, y = 1},
		{x = 1, y = 1}
	},
	
	-- Small shapes for variety
	-- Single tile
	SINGLE = {
		{x = 0, y = 0}
	},
	
	-- Two tiles horizontal
	DOUBLE_H = {
		{x = 0, y = 0},
		{x = 1, y = 0}
	},
	
	-- Two tiles vertical
	DOUBLE_V = {
		{x = 0, y = 0},
		{x = 0, y = 1}
	},
	
	-- Three tiles in line
	TRIPLE = {
		{x = 0, y = 0},
		{x = 1, y = 0},
		{x = 2, y = 0}
	},
	
	-- Corner piece (3 tiles)
	CORNER = {
		{x = 0, y = 0},
		{x = 1, y = 0},
		{x = 0, y = 1}
	}
}

-- Array of shape names for random selection
BlockShapes.SHAPE_NAMES = {}
for shapeName, _ in pairs(BlockShapes.SHAPES) do
	table.insert(BlockShapes.SHAPE_NAMES, shapeName)
end

-- Get a random shape definition
function BlockShapes:getRandomShape()
	local randomIndex = math.random(1, #self.SHAPE_NAMES)
	local shapeName = self.SHAPE_NAMES[randomIndex]
	
	-- Return a copy of the shape to prevent modification of original
	local shape = {}
	for _, tile in ipairs(self.SHAPES[shapeName]) do
		table.insert(shape, {x = tile.x, y = tile.y})
	end
	
	return shape, shapeName
end

-- Get a specific shape by name
function BlockShapes:getShape(shapeName)
	if not self.SHAPES[shapeName] then
		warn("BlockShapes:getShape - Unknown shape:", shapeName)
		return nil
	end
	
	-- Return a copy of the shape
	local shape = {}
	for _, tile in ipairs(self.SHAPES[shapeName]) do
		table.insert(shape, {x = tile.x, y = tile.y})
	end
	
	return shape
end

-- Get all available shape names
function BlockShapes:getShapeNames()
	local names = {}
	for _, name in ipairs(self.SHAPE_NAMES) do
		table.insert(names, name)
	end
	return names
end

-- Get shape bounds (useful for UI positioning)
function BlockShapes:getShapeBounds(shape)
	if not shape or #shape == 0 then
		return {minX = 0, maxX = 0, minY = 0, maxY = 0, width = 0, height = 0}
	end
	
	local minX, maxX = shape[1].x, shape[1].x
	local minY, maxY = shape[1].y, shape[1].y
	
	for _, tile in ipairs(shape) do
		minX = math.min(minX, tile.x)
		maxX = math.max(maxX, tile.x)
		minY = math.min(minY, tile.y)
		maxY = math.max(maxY, tile.y)
	end
	
	return {
		minX = minX,
		maxX = maxX,
		minY = minY,
		maxY = maxY,
		width = maxX - minX + 1,
		height = maxY - minY + 1
	}
end

-- Rotate a shape 90 degrees clockwise (for future rotation system)
function BlockShapes:rotateShape(shape)
	local rotatedShape = {}
	
	for _, tile in ipairs(shape) do
		-- Rotation formula: (x, y) -> (y, -x)
		table.insert(rotatedShape, {x = tile.y, y = -tile.x})
	end
	
	-- Normalize to ensure all coordinates are positive
	local bounds = self:getShapeBounds(rotatedShape)
	local offsetX = -bounds.minX
	local offsetY = -bounds.minY
	
	for _, tile in ipairs(rotatedShape) do
		tile.x = tile.x + offsetX
		tile.y = tile.y + offsetY
	end
	
	return rotatedShape
end

-- Validate that a shape definition is correct
function BlockShapes:validateShape(shape)
	if not shape or type(shape) ~= "table" or #shape == 0 then
		return false, "Shape must be a non-empty table"
	end
	
	for i, tile in ipairs(shape) do
		if type(tile) ~= "table" or type(tile.x) ~= "number" or type(tile.y) ~= "number" then
			return false, "Tile " .. i .. " must have numeric x and y coordinates"
		end
	end
	
	return true, "Shape is valid"
end

return BlockShapes
