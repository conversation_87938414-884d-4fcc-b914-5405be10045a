--[[
	Test Module for Tetris-like Puzzle Game
	Tests all modules to ensure they work correctly
	
	Author: Augment Agent
	Created: 2025-06-25
]]

local BlockShapes = require(script.Parent.Modules.BlockShapes)
local Grid = require(script.Parent.Modules.Grid)
local ShapeGenerator = require(script.Parent.Modules.ShapeGenerator)
local ScoreManager = require(script.Parent.Modules.ScoreManager)

local TestModules = {}

-- Test BlockShapes module
function TestModules:testBlockShapes()
	print("Testing BlockShapes module...")
	
	-- Test getting random shape
	local shape, shapeName = BlockShapes:getRandomShape()
	assert(shape ~= nil, "Random shape should not be nil")
	assert(shapeName ~= nil, "Shape name should not be nil")
	assert(type(shape) == "table", "Shape should be a table")
	assert(#shape > 0, "Shape should have at least one tile")
	
	-- Test getting specific shape
	local iShape = BlockShapes:getShape("I_PIECE")
	assert(iShape ~= nil, "I_PIECE should exist")
	assert(#iShape == 4, "I_PIECE should have 4 tiles")
	
	-- Test shape validation
	local isValid, message = BlockShapes:validateShape(iShape)
	assert(isValid, "I_PIECE should be valid: " .. (message or ""))
	
	-- Test shape bounds
	local bounds = BlockShapes:getShapeBounds(iShape)
	assert(bounds.width > 0, "Shape should have positive width")
	assert(bounds.height > 0, "Shape should have positive height")
	
	-- Test rotation
	local rotatedShape = BlockShapes:rotateShape(iShape)
	assert(#rotatedShape == #iShape, "Rotated shape should have same number of tiles")
	
	print("✓ BlockShapes module tests passed")
	return true
end

-- Test Grid module
function TestModules:testGrid()
	print("Testing Grid module...")
	
	local grid = Grid.new()
	
	-- Test initial state
	assert(not grid:isFilled(1, 1), "Grid should start empty")
	assert(not grid:isFilled(9, 9), "Grid should start empty")
	
	-- Test setting filled state
	assert(grid:setFilled(5, 5, true), "Should be able to set tile as filled")
	assert(grid:isFilled(5, 5), "Tile should be filled after setting")
	
	-- Test invalid coordinates
	assert(not grid:isFilled(0, 0), "Invalid coordinates should return false")
	assert(not grid:isFilled(10, 10), "Invalid coordinates should return false")
	
	-- Test tile position
	local position = grid:getTilePosition(1, 1)
	assert(position ~= nil, "Should get valid position for valid coordinates")
	assert(typeof(position) == "Vector3", "Position should be Vector3")
	
	-- Test shape placement
	local testShape = {{x = 0, y = 0}, {x = 1, y = 0}}
	assert(grid:canPlace(testShape, 1, 1), "Should be able to place shape on empty grid")
	
	local success = grid:place(testShape, 1, 1)
	assert(success, "Should successfully place shape")
	assert(grid:isFilled(1, 1), "Tile should be filled after placement")
	assert(grid:isFilled(2, 1), "Tile should be filled after placement")
	
	-- Test filled tiles
	local filledTiles = grid:getFilledTiles()
	assert(#filledTiles >= 2, "Should have at least 2 filled tiles")
	
	-- Clean up
	grid:destroy()
	
	print("✓ Grid module tests passed")
	return true
end

-- Test ShapeGenerator module
function TestModules:testShapeGenerator()
	print("Testing ShapeGenerator module...")
	
	local generator = ShapeGenerator.new()
	
	-- Test getting next shape
	local shape, shapeName = generator:getNextShape()
	assert(shape ~= nil, "Should get a shape")
	assert(shapeName ~= nil, "Should get a shape name")
	
	-- Test preview shapes
	local previews = generator:previewShapes()
	assert(type(previews) == "table", "Previews should be a table")
	assert(#previews <= 3, "Should have at most 3 preview shapes")
	
	-- Test queue status
	local status = generator:getQueueStatus()
	assert(status.queueSize >= 0, "Queue size should be non-negative")
	assert(type(status.nextShapes) == "table", "Next shapes should be a table")
	
	print("✓ ShapeGenerator module tests passed")
	return true
end

-- Test ScoreManager module
function TestModules:testScoreManager()
	print("Testing ScoreManager module...")
	
	local scoreManager = ScoreManager.new()
	
	-- Test initial score
	assert(scoreManager:getScore() == 0, "Initial score should be 0")
	assert(scoreManager:getHighScore() == 0, "Initial high score should be 0")
	
	-- Test adding score
	assert(scoreManager:add(100), "Should be able to add score")
	assert(scoreManager:getScore() == 100, "Score should be updated")
	
	-- Test tile placement scoring
	local points = scoreManager:scoreTilePlacement(4)
	assert(points > 0, "Should get points for tile placement")
	assert(scoreManager:getScore() > 100, "Score should increase")
	
	-- Test line clearing scoring
	local linePoints = scoreManager:scoreLineClearing(2)
	assert(linePoints > 0, "Should get points for line clearing")
	
	-- Test statistics
	local stats = scoreManager:getStats()
	assert(type(stats) == "table", "Stats should be a table")
	assert(stats.currentScore > 0, "Current score should be positive")
	assert(stats.shapesPlaced > 0, "Should have placed shapes")
	
	-- Test reset
	scoreManager:resetGame()
	assert(scoreManager:getScore() == 0, "Score should reset to 0")
	
	print("✓ ScoreManager module tests passed")
	return true
end

-- Run all tests
function TestModules:runAllTests()
	print("Running all module tests...")
	print("=" .. string.rep("=", 50))
	
	local tests = {
		self.testBlockShapes,
		self.testGrid,
		self.testShapeGenerator,
		self.testScoreManager
	}
	
	local passed = 0
	local total = #tests
	
	for i, test in ipairs(tests) do
		local success, error = pcall(test, self)
		if success then
			passed = passed + 1
		else
			print("✗ Test " .. i .. " failed:", error)
		end
	end
	
	print("=" .. string.rep("=", 50))
	print("Test Results:", passed .. "/" .. total .. " tests passed")
	
	if passed == total then
		print("🎉 All tests passed! The Tetris game modules are working correctly.")
	else
		print("❌ Some tests failed. Please check the errors above.")
	end
	
	return passed == total
end

return TestModules
