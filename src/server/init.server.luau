--[[
	Main Server Script for Tetris-like Puzzle Game
	Initializes server-side systems and game management

	Author: Augment Agent
	Created: 2025-06-25
]]

print("Tetris Game Server Starting...")

-- The GameManager will handle all server-side initialization
-- This script just ensures the GameManager starts up
local GameManager = require(script:WaitForChild("GameManager"))

print("Tetris Game Server Initialized!")