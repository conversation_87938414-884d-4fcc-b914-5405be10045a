--[[
	GameManager Server Script for Tetris-like Puzzle Game
	Manages game state, placement validation, and game over detection
	
	Author: Augment Agent
	Created: 2025-06-25
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Wait for modules to load
local Modules = ReplicatedStorage:WaitForChild("Modules")
local Grid = require(Modules:WaitForChild("Grid"))
local BlockShapes = require(Modules:WaitForChild("BlockShapes"))
local ShapeGenerator = require(Modules:WaitForChild("ShapeGenerator"))
local ScoreManager = require(Modules:WaitForChild("ScoreManager"))
local Events = require(ReplicatedStorage:WaitForChild("Events"))

local GameManager = {}

-- Game state
local gameGrid = nil
local shapeGenerator = nil
local scoreManager = nil
local gameState = "WAITING" -- WAITING, PLAYING, GAME_OVER, PAUSED
local connectedPlayers = {}

-- Initialize the game manager
function GameManager:initialize()
	print("Initializing GameManager...")
	
	-- Initialize events
	Events:initializeRemoteEvents()
	Events:initializeBindableEvents()
	
	-- Initialize game systems
	gameGrid = Grid.new()
	shapeGenerator = ShapeGenerator.new()
	scoreManager = ScoreManager.new()
	scoreManager:initializeRemoteEvents()
	
	-- Connect to events
	self:connectEvents()
	
	-- Start the game
	self:startGame()
	
	print("GameManager initialized successfully")
end

-- Connect to all relevant events
function GameManager:connectEvents()
	-- Player connection events
	Players.PlayerAdded:Connect(function(player)
		self:onPlayerJoined(player)
	end)
	
	Players.PlayerRemoving:Connect(function(player)
		self:onPlayerLeft(player)
	end)
	
	-- Shape placement events
	Events:connectRemoteEventServer("PlaceShape", function(player, shapeData)
		self:handleShapePlacement(player, shapeData)
	end)
	
	-- Game control events
	Events:connectRemoteEventServer("GameRestart", function(player)
		self:restartGame(player)
	end)
	
	Events:connectRemoteEventServer("GamePause", function(player)
		self:pauseGame(player)
	end)
	
	-- Internal events
	Events:connect("LinesCleared", function(linesCleared)
		self:handleLinesCleared(linesCleared)
	end)
end

-- Handle player joining
function GameManager:onPlayerJoined(player)
	print("Player joined:", player.Name)
	connectedPlayers[player] = true
	
	-- Send current game state to new player
	self:sendGameStateToPlayer(player)
	
	-- Send next shapes preview
	self:sendShapePreviewToPlayer(player)
end

-- Handle player leaving
function GameManager:onPlayerLeft(player)
	print("Player left:", player.Name)
	connectedPlayers[player] = nil
end

-- Send current game state to a player
function GameManager:sendGameStateToPlayer(player)
	local gameData = {
		state = gameState,
		grid = gameGrid:getFilledTiles(),
		score = scoreManager:getScore(),
		highScore = scoreManager:getHighScore(),
		stats = scoreManager:getStats()
	}
	
	Events:fireClient("UpdateGrid", player, gameData)
end

-- Send shape preview to a player
function GameManager:sendShapePreviewToPlayer(player)
	local previews = shapeGenerator:previewShapes()
	Events:fireClient("UpdatePreview", player, previews)
end

-- Start a new game
function GameManager:startGame()
	print("Starting new game...")
	
	gameState = "PLAYING"
	
	-- Reset game systems
	if gameGrid then
		gameGrid:destroy()
	end
	gameGrid = Grid.new()
	
	shapeGenerator:regenerateQueue()
	scoreManager:resetGame()
	
	-- Notify all players
	Events:fireAllClients("GameStart", {
		grid = gameGrid:getFilledTiles(),
		previews = shapeGenerator:previewShapes()
	})
	
	print("Game started successfully")
end

-- Handle shape placement request
function GameManager:handleShapePlacement(player, shapeData)
	if gameState ~= "PLAYING" then
		Events:fireClient("PlaceShape", player, false, "Game is not active")
		return
	end
	
	if not shapeData or not shapeData.shape or not shapeData.gridX or not shapeData.gridY then
		Events:fireClient("PlaceShape", player, false, "Invalid shape data")
		return
	end
	
	local shape = shapeData.shape
	local gridX = shapeData.gridX
	local gridY = shapeData.gridY
	local shapeName = shapeData.shapeName or "Unknown"
	
	-- Validate placement
	if not gameGrid:canPlace(shape, gridX, gridY) then
		Events:fireClient("PlaceShape", player, false, "Cannot place shape at this position")
		return
	end
	
	-- Place the shape
	local success, placedParts = gameGrid:place(shape, gridX, gridY)
	if not success then
		Events:fireClient("PlaceShape", player, false, "Failed to place shape")
		return
	end
	
	print("Shape placed:", shapeName, "at", gridX, gridY)
	
	-- Score the placement
	local tilesPlaced = #shape
	scoreManager:scoreTilePlacement(tilesPlaced)
	
	-- Check for line clears
	local linesCleared, clearedParts = gameGrid:clearFullLines()
	if linesCleared > 0 then
		print("Lines cleared:", linesCleared)
		scoreManager:scoreLineClearing(linesCleared)
		Events:fire("LinesCleared", linesCleared)
	else
		scoreManager:resetCombo()
	end
	
	-- Update all clients
	Events:fireAllClients("UpdateGrid", {
		grid = gameGrid:getFilledTiles(),
		score = scoreManager:getScore(),
		stats = scoreManager:getStats()
	})
	
	-- Send success response to placing player
	Events:fireClient("PlaceShape", player, true, "Shape placed successfully")
	
	-- Send new shape preview
	local previews = shapeGenerator:previewShapes()
	Events:fireAllClients("UpdatePreview", previews)
	
	-- Check for game over
	self:checkGameOver()
end

-- Check if the game is over
function GameManager:checkGameOver()
	if gameState ~= "PLAYING" then
		return
	end
	
	-- Get all available shapes
	local shapeNames = BlockShapes:getShapeNames()
	local canPlaceAnyShape = false
	
	-- Check if any shape can be placed anywhere on the grid
	for _, shapeName in ipairs(shapeNames) do
		local shape = BlockShapes:getShape(shapeName)
		if shape then
			-- Try every position on the grid
			for x = 1, 9 do
				for y = 1, 9 do
					if gameGrid:canPlace(shape, x, y) then
						canPlaceAnyShape = true
						break
					end
				end
				if canPlaceAnyShape then break end
			end
		end
		if canPlaceAnyShape then break end
	end
	
	-- Also check the current preview shapes
	if not canPlaceAnyShape then
		local previews = shapeGenerator:previewShapes()
		for _, preview in ipairs(previews) do
			local shape = preview.shape
			for x = 1, 9 do
				for y = 1, 9 do
					if gameGrid:canPlace(shape, x, y) then
						canPlaceAnyShape = true
						break
					end
				end
				if canPlaceAnyShape then break end
			end
			if canPlaceAnyShape then break end
		end
	end
	
	if not canPlaceAnyShape then
		self:triggerGameOver()
	end
end

-- Trigger game over
function GameManager:triggerGameOver()
	print("Game Over!")
	
	gameState = "GAME_OVER"
	
	local finalStats = scoreManager:getStats()
	
	-- Notify all players
	Events:fireAllClients("GameOver", {
		finalScore = finalStats.currentScore,
		highScore = finalStats.highScore,
		stats = finalStats,
		reason = "No valid moves remaining"
	})
	
	-- Auto-restart after delay (optional)
	wait(5)
	if gameState == "GAME_OVER" then
		self:startGame()
	end
end

-- Restart the game
function GameManager:restartGame(requestingPlayer)
	print("Game restart requested by:", requestingPlayer and requestingPlayer.Name or "Server")
	
	self:startGame()
end

-- Pause the game
function GameManager:pauseGame(requestingPlayer)
	if gameState == "PLAYING" then
		gameState = "PAUSED"
		Events:fireAllClients("GamePause", true)
		print("Game paused by:", requestingPlayer.Name)
	elseif gameState == "PAUSED" then
		gameState = "PLAYING"
		Events:fireAllClients("GamePause", false)
		print("Game resumed by:", requestingPlayer.Name)
	end
end

-- Handle lines cleared event
function GameManager:handleLinesCleared(linesCleared)
	print("Handling", linesCleared, "lines cleared")
	
	-- Could add special effects, achievements, etc. here
	if linesCleared >= 3 then
		print("Multi-line clear bonus!")
		-- Could trigger special effects
	end
end

-- Get current game status
function GameManager:getGameStatus()
	return {
		state = gameState,
		playerCount = 0, -- Count connected players
		score = scoreManager:getScore(),
		highScore = scoreManager:getHighScore(),
		filledTiles = #gameGrid:getFilledTiles(),
		totalTiles = 81 -- 9x9 grid
	}
end

-- Cleanup function
function GameManager:cleanup()
	if gameGrid then
		gameGrid:destroy()
	end
	
	Events:cleanup()
	
	print("GameManager cleaned up")
end

-- Initialize the game manager
GameManager:initialize()

-- Handle server shutdown
game:BindToClose(function()
	GameManager:cleanup()
end)

return GameManager
